//----------------------------------------------
//            Realistic Car Controller
//
// Copyright © 2014 - 2024 BoneCracker Games
// https://www.bonecrackergames.com
// <PERSON><PERSON><PERSON>
//
//----------------------------------------------

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

/// <summary>
/// Truck trailer has additional wheelcolliders. This script handles center of mass of the trailer, wheelcolliders, ligths, etc...
/// </summary>
[RequireComponent(typeof(Rigidbody))]
[RequireComponent(typeof(ConfigurableJoint))]
public class RCC_TruckTrailer : MonoBehaviour {

    private RCC_CarControllerV4 carController;      //  Car controller of this trailer.
    private Rigidbody rigid;        //  Rigidbody.
    private ConfigurableJoint joint;        //  Configurable joint of this trailer.

    [Header("High-Speed Stability Settings (300 km/h Optimized)")]
    [Range(0.5f, 3f)] public float stabilityMultiplier = 2.2f;     // Multiplier for stability at high speeds
    [Range(0.1f, 1f)] public float speedBasedStiffness = 0.95f;    // Speed-based friction adjustment
    [Range(0f, 350f)] public float maxStabilitySpeed = 300f;       // Speed at which max stability is applied (300 km/h)
    [Range(0.1f, 5f)] public float angularDragMultiplier = 3.5f;   // Additional angular drag for stability

    [Header("Advanced Stability Controls")]
    [Range(0.1f, 2f)] public float downforceMultiplier = 1.8f;     // Downforce at high speeds for better grip
    [Range(0f, 10000f)] public float antiSwayForce = 8000f;        // Force to counter lateral movement
    [Range(0f, 5000f)] public float stabilizingTorque = 3500f;     // Torque to prevent excessive rotation
    [Range(0.1f, 3f)] public float jointStiffnessMultiplier = 2.5f; // Joint stiffness at high speeds
    [Range(0f, 1f)] public float speedThreshold = 0.3f;            // Speed threshold for stability activation (30% of max speed)

    /// <summary>
    /// Wheel colliders and models.
    /// </summary>
    [System.Serializable]
    public class TrailerWheel {

        public WheelCollider wheelCollider;
        public Transform wheelModel;

        private WheelFrictionCurve originalForwardFriction;
        private WheelFrictionCurve originalSidewaysFriction;
        private bool originalFrictionsStored = false;

        public void Torque(float torque) {

            wheelCollider.motorTorque = torque;

        }

        public void Brake(float torque) {

            wheelCollider.brakeTorque = torque;

        }

        public void StoreOriginalFrictions() {
            if (!originalFrictionsStored) {
                originalForwardFriction = wheelCollider.forwardFriction;
                originalSidewaysFriction = wheelCollider.sidewaysFriction;
                originalFrictionsStored = true;
            }
        }

        public void ApplySpeedBasedFriction(float speed, float stabilityMultiplier, float speedBasedStiffness, float maxStabilitySpeed, float downforceMultiplier) {
            if (!originalFrictionsStored) return;

            float speedRatio = Mathf.Clamp01(speed / maxStabilitySpeed);
            float stabilityFactor = Mathf.Lerp(1f, stabilityMultiplier, speedRatio * speedBasedStiffness);

            // Enhanced downforce effect for 300 km/h stability
            float downforceFactor = 1f + (speedRatio * downforceMultiplier);

            WheelFrictionCurve forwardFriction = originalForwardFriction;
            WheelFrictionCurve sidewaysFriction = originalSidewaysFriction;

            // Increase stiffness for better grip at higher speeds with downforce effect
            forwardFriction.stiffness = originalForwardFriction.stiffness * stabilityFactor * downforceFactor;
            sidewaysFriction.stiffness = originalSidewaysFriction.stiffness * stabilityFactor * downforceFactor;

            // Progressive slip adjustment for high-speed stability
            float slipReduction = speedRatio * 0.4f; // Increased from 0.3f for better high-speed control
            sidewaysFriction.extremumSlip = originalSidewaysFriction.extremumSlip * (1f - slipReduction);
            sidewaysFriction.asymptoteSlip = originalSidewaysFriction.asymptoteSlip * (1f - speedRatio * 0.3f);

            // Improve forward friction for better acceleration/deceleration at high speeds
            forwardFriction.extremumSlip = originalForwardFriction.extremumSlip * (1f - speedRatio * 0.2f);
            forwardFriction.asymptoteSlip = originalForwardFriction.asymptoteSlip * (1f - speedRatio * 0.15f);

            wheelCollider.forwardFriction = forwardFriction;
            wheelCollider.sidewaysFriction = sidewaysFriction;
        }

    }
    public TrailerWheel[] trailerWheels;        //  All trailer wheels.

    public Transform COM;       //  Center of mass.
    public GameObject legs;     //  Legs will be enabled when trailer is detached.
    private bool isSleeping = false;        //  Is rigidbody of the trailer is sleeping?

    private float timer = 0f;       //  Timer for attach / detach process.
    public bool attached = false;       //  Is this trailer attached now?

    public bool brakeWhenDetached = false;
    public float brakeForce = 5000f;

    private float originalAngularDrag;      // Store original angular drag value

    /// <summary>
    /// Joint restrictions of the trailer.
    /// </summary>
    private class JointRestrictions {

        public ConfigurableJointMotion motionX;
        public ConfigurableJointMotion motionY;
        public ConfigurableJointMotion motionZ;

        public ConfigurableJointMotion angularMotionX;
        public ConfigurableJointMotion angularMotionY;
        public ConfigurableJointMotion angularMotionZ;

        public void Get(ConfigurableJoint configurableJoint) {

            motionX = configurableJoint.xMotion;
            motionY = configurableJoint.yMotion;
            motionZ = configurableJoint.zMotion;

            angularMotionX = configurableJoint.angularXMotion;
            angularMotionY = configurableJoint.angularYMotion;
            angularMotionZ = configurableJoint.angularZMotion;

        }

        public void Set(ConfigurableJoint configurableJoint) {

            configurableJoint.xMotion = motionX;
            configurableJoint.yMotion = motionY;
            configurableJoint.zMotion = motionZ;

            configurableJoint.angularXMotion = angularMotionX;
            configurableJoint.angularYMotion = angularMotionY;
            configurableJoint.angularZMotion = angularMotionZ;

        }

        public void Reset(ConfigurableJoint configurableJoint) {

            configurableJoint.xMotion = ConfigurableJointMotion.Free;
            configurableJoint.yMotion = ConfigurableJointMotion.Free;
            configurableJoint.zMotion = ConfigurableJointMotion.Free;

            configurableJoint.angularXMotion = ConfigurableJointMotion.Free;
            configurableJoint.angularYMotion = ConfigurableJointMotion.Free;
            configurableJoint.angularZMotion = ConfigurableJointMotion.Free;

        }

    }
    private JointRestrictions jointRestrictions = new JointRestrictions();
    private RCC_Light[] lights;

    private void Start() {

        rigid = GetComponent<Rigidbody>();      //	Getting rigidbody.
        joint = GetComponentInParent<ConfigurableJoint>(true);      //	Getting configurable joint.
        jointRestrictions.Get(joint);       //	Getting current limitations of the joint.

        // Store original angular drag for stability calculations
        originalAngularDrag = rigid.angularDamping;

        // Initialize wheel friction settings
        foreach (TrailerWheel wheel in trailerWheels) {
            wheel.StoreOriginalFrictions();
        }

        // Fixing stutering bug of the rigid.
        rigid.interpolation = RigidbodyInterpolation.None;
        rigid.interpolation = RigidbodyInterpolation.Interpolate;
        joint.configuredInWorldSpace = true;

        // Enhanced joint stability for 300 km/h performance
        if (joint != null) {
            // Significantly increase angular drive damping for high-speed stability
            var angularDrive = joint.angularYZDrive;
            angularDrive.positionDamper = 12000f;  // Increased from 5000f
            angularDrive.maximumForce = 25000f;    // Increased from 10000f
            angularDrive.positionSpring = 8000f;   // Added spring for better response
            joint.angularYZDrive = angularDrive;

            // Configure linear drive for better stability
            var linearDrive = joint.xDrive;
            linearDrive.positionDamper = 8000f;
            linearDrive.maximumForce = 15000f;
            joint.xDrive = linearDrive;
            joint.zDrive = linearDrive;

            // Set projection settings for better joint stability at high speeds
            joint.projectionMode = JointProjectionMode.PositionAndRotation;
            joint.projectionDistance = 0.1f;
            joint.projectionAngle = 2f;
        }

        //	If joint is connected as default, attach the trailer. Otherwise detach.
        if (joint.connectedBody) {

            AttachTrailer(joint.connectedBody.gameObject.GetComponent<RCC_CarControllerV4>());

        } else {

            carController = null;
            joint.connectedBody = null;
            jointRestrictions.Reset(joint);

        }

    }

    private void FixedUpdate() {

        attached = joint.connectedBody;     //	Is trailer attached now?
        rigid.centerOfMass = transform.InverseTransformPoint(COM.transform.position);       //	Setting center of mass.

        // Apply speed-based stability improvements
        if (attached && carController) {
            ApplyStabilityControl();
        }

        //	Applying torque to the wheels.
        for (int i = 0; i < trailerWheels.Length; i++) {

            if (carController) {

                trailerWheels[i].Torque(carController.throttleInput * (attached ? 1f : 0f));
                trailerWheels[i].Brake((attached ? 0f : 5000f));

                // Apply enhanced speed-based friction adjustments for 300 km/h stability
                if (attached) {
                    float currentSpeed = rigid.linearVelocity.magnitude * 3.6f; // Convert to km/h
                    trailerWheels[i].ApplySpeedBasedFriction(currentSpeed, stabilityMultiplier, speedBasedStiffness, maxStabilitySpeed, downforceMultiplier);
                }

            } else {

                trailerWheels[i].Torque(0f);
                trailerWheels[i].Brake((brakeWhenDetached ? brakeForce : 0f));

            }

        }

    }

    private void Update() {

        //	If trailer is not moving, enable sleeping mode.
        if (rigid.linearVelocity.magnitude < .01f && Mathf.Abs(rigid.angularVelocity.magnitude) < .01f)
            isSleeping = true;
        else
            isSleeping = false;

        // Timer was used for attach/detach delay.
        if (timer > 0f)
            timer -= Time.deltaTime;

        timer = Mathf.Clamp01(timer);       //	Clamping timer between 0f - 1f.

        WheelAlign();  // Aligning wheel model position and rotation.

    }

    /// <summary>
    /// Aligning wheel model position and rotation.
    /// </summary>
    private void WheelAlign() {

        //	If trailer is sleeping, return.
        if (isSleeping)
            return;

        for (int i = 0; i < trailerWheels.Length; i++) {

            // Return if no wheel model selected.
            if (!trailerWheels[i].wheelModel) {

                Debug.LogError(transform.name + " wheel of the " + transform.name + " is missing wheel model. This wheel is disabled");
                enabled = false;
                return;

            }

            // Locating correct position and rotation for the wheel.
            Vector3 wheelPosition;
            Quaternion wheelRotation;

            trailerWheels[i].wheelCollider.GetWorldPose(out wheelPosition, out wheelRotation);

            //	Assigning position and rotation to the wheel model.
            trailerWheels[i].wheelModel.transform.SetPositionAndRotation(wheelPosition, wheelRotation);

        }

    }

    /// <summary>
    /// Enhanced stability control optimized for 300 km/h performance.
    /// </summary>
    private void ApplyStabilityControl() {

        if (!carController) return;

        float currentSpeed = rigid.linearVelocity.magnitude * 3.6f; // Convert to km/h
        float speedRatio = Mathf.Clamp01(currentSpeed / maxStabilitySpeed);
        float speedThresholdActivation = maxStabilitySpeed * speedThreshold; // 30% of max speed

        // Progressive angular drag increase for high-speed stability
        float targetAngularDrag = originalAngularDrag + (angularDragMultiplier * speedRatio * speedRatio); // Quadratic increase
        rigid.angularDamping = Mathf.Lerp(rigid.angularDamping, targetAngularDrag, Time.fixedDeltaTime * 3f);

        // Apply downforce effect for better grip at high speeds
        if (currentSpeed > speedThresholdActivation) {
            float downforceEffect = speedRatio * downforceMultiplier * 2000f;
            Vector3 downforce = -transform.up * downforceEffect;
            rigid.AddForce(downforce, ForceMode.Force);
        }

        // Enhanced anti-sway system for high-speed turns
        if (currentSpeed > speedThresholdActivation && Mathf.Abs(carController.steerInput) > 0.05f) {

            Vector3 lateralVelocity = Vector3.Project(rigid.linearVelocity, transform.right);
            float swayMagnitude = lateralVelocity.magnitude;

            if (swayMagnitude > 0.1f) {
                // Progressive anti-sway force based on speed and lateral movement
                float swayForceMultiplier = speedRatio * antiSwayForce;
                Vector3 antiSwayForce = -lateralVelocity.normalized * swayForceMultiplier;
                rigid.AddForce(antiSwayForce, ForceMode.Force);

                // Additional stabilization at very high speeds (above 200 km/h)
                if (currentSpeed > 200f) {
                    Vector3 extraStabilization = -lateralVelocity * speedRatio * 1500f;
                    rigid.AddForce(extraStabilization, ForceMode.Force);
                }
            }

            // Enhanced stabilizing torque system
            Vector3 angularVel = rigid.angularVelocity;
            float angularVelocityY = angularVel.y;
            float angularVelocityX = angularVel.x;
            float angularVelocityZ = angularVel.z;

            // Y-axis (yaw) stabilization
            if (Mathf.Abs(angularVelocityY) > 0.3f) {
                float yawTorque = -angularVelocityY * speedRatio * stabilizingTorque;
                rigid.AddTorque(new Vector3(0, yawTorque, 0), ForceMode.Force);
            }

            // X-axis (pitch) stabilization for high-speed stability
            if (Mathf.Abs(angularVelocityX) > 0.2f && currentSpeed > 150f) {
                float pitchTorque = -angularVelocityX * speedRatio * stabilizingTorque * 0.5f;
                rigid.AddTorque(new Vector3(pitchTorque, 0, 0), ForceMode.Force);
            }

            // Z-axis (roll) stabilization
            if (Mathf.Abs(angularVelocityZ) > 0.2f) {
                float rollTorque = -angularVelocityZ * speedRatio * stabilizingTorque * 0.7f;
                rigid.AddTorque(new Vector3(0, 0, rollTorque), ForceMode.Force);
            }
        }

        // Dynamic joint stability adjustment based on speed
        if (joint && currentSpeed > speedThresholdActivation) {
            var angularDrive = joint.angularYZDrive;
            float stabilityFactor = 1f + (speedRatio * jointStiffnessMultiplier);

            angularDrive.positionDamper = 12000f * stabilityFactor;
            angularDrive.maximumForce = 25000f * stabilityFactor;
            angularDrive.positionSpring = 8000f * stabilityFactor;
            joint.angularYZDrive = angularDrive;

            // Enhanced linear drive stability at very high speeds
            if (currentSpeed > 200f) {
                var linearDrive = joint.xDrive;
                linearDrive.positionDamper = 8000f * stabilityFactor;
                linearDrive.maximumForce = 15000f * stabilityFactor;
                joint.xDrive = linearDrive;
                joint.zDrive = linearDrive;
            }
        }
    }

    /// <summary>
    /// Detach the trailer.
    /// </summary>
    public void DetachTrailer() {

        foreach (RCC_Light item in lights) {

            item.CarController = null;

        }

        // Resetting attachedTrailer of car controller.
        carController.attachedTrailer = null;
        carController = null;
        lights = null;
        timer = 1f;
        joint.connectedBody = null;
        jointRestrictions.Reset(joint);

        if (legs)
            legs.SetActive(true);

        if (RCC_SceneManager.Instance.activePlayerCamera && RCC_SceneManager.Instance.activePlayerCamera.TPSAutoFocus)
            StartCoroutine(RCC_SceneManager.Instance.activePlayerCamera.AutoFocus());

    }

    /// <summary>
    /// Attach the trailer.
    /// </summary>
    /// <param name="vehicle"></param>
    public void AttachTrailer(RCC_CarControllerV4 vehicle) {

        // If delay is short, return.
        if (timer > 0)
            return;

        carController = vehicle;        //	Assigning car controller.
        lights = GetComponentsInChildren<RCC_Light>();       //	Getting car controller lights.
        timer = 1f;     //	Setting timer.

        joint.connectedBody = vehicle.Rigid;        //	Connecting joint.
                                                    //joint.autoConfigureConnectedAnchor = false;		//	Setting auto configuration off of the joint.
                                                    //Vector3 jointVector = joint.connectedAnchor;		//	Resetting X axis of the connected anchor on attachment.
                                                    //jointVector.x = 0f;
                                                    //joint.connectedAnchor = jointVector;
        jointRestrictions.Set(joint);       //	Enabling limitations of the joint.

        // Reset angular drag to original value when attaching
        rigid.angularDamping = originalAngularDrag;

        // If trailer has legs, disable on attach.
        if (legs)
            legs.SetActive(false);

        //	Initializing lights of the trailer. Parent car controller will take control of them.
        foreach (RCC_Light item in lights) {

            item.CarController = carController;

        }

        // Assigning attachedTrailer of car controller.
        vehicle.attachedTrailer = this;
        rigid.isKinematic = false;

        // If autofocus is enabled on RCC Camera, run it.
        if (RCC_SceneManager.Instance.activePlayerCamera && RCC_SceneManager.Instance.activePlayerCamera.TPSAutoFocus)
            StartCoroutine(RCC_SceneManager.Instance.activePlayerCamera.AutoFocus(transform, carController.transform));

    }

    private void Reset() {

        if (COM == null) {

            GameObject com = new GameObject("COM");
            com.transform.SetParent(transform, false);
            com.transform.localPosition = Vector3.zero;
            com.transform.localRotation = Quaternion.identity;
            com.transform.localScale = Vector3.one;
            COM = com.transform;

        }

        if (transform.Find("Wheel Models") == null) {

            GameObject com = new GameObject("Wheel Models");
            com.transform.SetParent(transform, false);
            com.transform.localPosition = Vector3.zero;
            com.transform.localRotation = Quaternion.identity;
            com.transform.localScale = Vector3.one;

        }

        if (transform.Find("Wheel Colliders") == null) {

            GameObject com = new GameObject("Wheel Colliders");
            com.transform.SetParent(transform, false);
            com.transform.localPosition = Vector3.zero;
            com.transform.localRotation = Quaternion.identity;
            com.transform.localScale = Vector3.one;

        }

        GetComponent<Rigidbody>().mass = 5000;

    }

}
